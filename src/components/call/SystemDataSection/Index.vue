<template>
  <section id="system-data-section">
    <div class="_header">
      <div class="_subheader">System Data</div>
      <app-button class="_add" @click="addItem" :disabled="!canEditSystemData">
        <i class="far fa-plus"></i>
      </app-button>
    </div>

    <SystemDataList :tags="systemTagsProxy" />
  </section>
</template>

<script>
import Access from '@/utils/access.js';
import BaseSection from '../BaseSection.vue';
import SystemDataList from './SystemTagList.vue';

import {
  CALL_SECTION_SYSTEM_DATA,
  // BEFORE_CALL_READ
} from '@/config.js';

export default {
  name: CALL_SECTION_SYSTEM_DATA,

  extends: BaseSection,

  components: {
    SystemDataList
  },

  data () {
    return {
      sectionName: CALL_SECTION_SYSTEM_DATA,

      systemTags: []
    };
  },

  computed: {
    canEditSystemData () {
      return Access.has('calls.editSystemData') && this.canEditProperty();
    },

    systemTagsProxy: {
      get() {
        return this.parseSystemData(this.call?.vc255DelimitedInfo);
      },
      set(value) {
        this.$set(this.call, 'vc255DelimitedInfo', this.flattenSystemData(value));
      }
    }
  },

  methods: {
    parseSystemData (value = '') {
      if (value.length < 3) return [];

      const pairs = value.split(';');
      let items = [];

      pairs.forEach(pair => {
        const [key, value] = [...pair.split('=')];

        items.push({ key, value });
      });

      return items;
    },

    flattenSystemData (value) {
      let pairs = [];

      value.forEach(item => {
        pairs.push([item.key, item.value].join('='));
      });

      return pairs.join(';');
    },

    addItem () {
      this.systemTags.push({
        key: '',
        value: ''
      });
    }
  }
};
</script>

<style scoped>
#data-tags-section {
  ._header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    padding: 0.25rem 0.5rem;
    border-bottom: 1px solid var(--input-border);
    background-color: hsl(var(--blue-h), var(--blue-s), calc(var(--blue-l) + 55%));
    overflow: hidden;

    ._add {
      width: 1rem;
      font-size: var(--font-size-h5);
      background-color: transparent;
      border: 0;
    }
  }

  ._subheader {
    font-weight: bold;
  }

  ._remove {
    display: flex;
    justify-content: center;
    align-items: center;

    cursor: pointer;
  }
}
</style>
