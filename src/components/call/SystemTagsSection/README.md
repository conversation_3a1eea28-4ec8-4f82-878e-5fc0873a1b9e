# SystemTagsSection

This component manages the System Data Tags functionality for call records.

## Purpose

The SystemTagsSection component handles:
- Displaying system data tags associated with a call
- Adding new system data tags
- Editing existing system data tags
- Removing system data tags
- Synchronizing tags with the call's `vc255DelimitedInfo` field

## Usage

```vue
<SystemTagList v-model="call.vc255DelimitedInfo" />
```

## Components

### SystemTagList
The main component that handles the v-model interface with the delimited string.

**Props:**
- `modelValue` (String): The delimited string of system tags

**Events:**
- `update:modelValue`: Emitted when the internal tag array changes

### SystemTagItem
Individual tag editor component.

**Props:**
- `tag` (Object): The tag object with `key` and `value` properties
- `index` (Number): The index of the tag in the array
- `availableTags` (Array): Available system tags from the API

**Events:**
- `remove-tag`: Emitted when the tag should be removed
- `update-tag`: Emitted when the tag is modified

## Features

- **v-model Support**: The SystemTagList component properly implements v-model for seamless integration
- **Internal Array Management**: Parses delimited strings into arrays internally for easier manipulation
- **Dynamic Input Types**: Automatically determines whether to use text input or select dropdown based on available options for each tag
- **Real-time Synchronization**: Changes to tags are automatically synchronized with the parent's delimited string
- **Access Control**: Respects user permissions for editing system data tags
- **Validation**: Ensures only valid data tags can be selected
- **Empty Tag Filtering**: Automatically filters out empty tags when flattening to string

## Data Structure

System data tags are stored as a semicolon-delimited string of key=value pairs:
```
key1=value1;key2=value2;key3=value3
```

The SystemTagList component:
1. **Parses** this string into an array of objects: `[{key: 'key1', value: 'value1'}, ...]`
2. **Manages** the array internally for easier manipulation
3. **Flattens** the array back to a delimited string when emitting changes
4. **Filters** out empty tags during flattening to keep the string clean

## Implementation Details

The refactoring provides:
- **Clean v-model interface**: Parent components can use `v-model` naturally
- **Internal complexity hiding**: String parsing/flattening is handled internally
- **Reactive updates**: Changes propagate correctly through Vue's reactivity system
- **Type safety**: Proper TypeScript-like prop validation
- **Test coverage**: Comprehensive unit tests ensure reliability
