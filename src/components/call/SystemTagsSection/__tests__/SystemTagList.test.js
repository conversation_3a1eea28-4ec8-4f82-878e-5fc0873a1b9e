import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import SystemTagList from '../SystemTagList.vue';

// Mock the tops utility
vi.mock('@/utils/tops', () => ({
  default: {
    TOPSCompany: {
      GetSystemDataTags: vi.fn().mockResolvedValue({
        data: {
          Data: [
            { ShortCode: 'TAG1', Name: 'Tag 1', Options: [] },
            { ShortCode: 'TAG2', Name: 'Tag 2', Options: ['Option1', 'Option2'] }
          ]
        }
      })
    }
  }
}));

// Mock Access utility
vi.mock('@/utils/access.js', () => ({
  default: {
    has: vi.fn().mockReturnValue(true)
  }
}));

describe('SystemTagList', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = mount(SystemTagList, {
      props: {
        modelValue: ''
      },
      global: {
        stubs: {
          'app-grid-form': { template: '<div><slot /></div>' },
          'app-button': { template: '<button><slot /></button>' },
          'SystemTagItem': {
            template: '<div class="tag-item"></div>',
            props: ['tag', 'index', 'availableTags'],
            emits: ['remove-tag', 'update-tag']
          }
        }
      }
    });
  });

  it('should parse delimited string into internal array', async () => {
    await wrapper.setProps({ modelValue: 'key1=value1;key2=value2' });

    expect(wrapper.vm.internalTags.value).toEqual([
      { key: 'key1', value: 'value1' },
      { key: 'key2', value: 'value2' }
    ]);
  });

  it('should handle empty string', async () => {
    await wrapper.setProps({ modelValue: '' });

    expect(wrapper.vm.internalTags.value).toEqual([]);
  });

  it('should handle malformed strings gracefully', async () => {
    await wrapper.setProps({ modelValue: 'key1=value1;malformed;key2=value2' });

    expect(wrapper.vm.internalTags.value).toEqual([
      { key: 'key1', value: 'value1' },
      { key: 'key2', value: 'value2' }
    ]);
  });

  it('should emit flattened string when internal array changes', async () => {
    wrapper.vm.internalTags.value = [
      { key: 'newkey', value: 'newvalue' },
      { key: 'key2', value: 'value2' }
    ];

    await wrapper.vm.$nextTick();

    expect(wrapper.emitted('update:modelValue')).toBeTruthy();
    expect(wrapper.emitted('update:modelValue')[0][0]).toBe('newkey=newvalue;key2=value2');
  });

  it('should add new tag when addTag is called', async () => {
    wrapper.vm.addTag();

    expect(wrapper.vm.internalTags.value).toContainEqual({ key: '', value: '' });
  });

  it('should remove tag when removeTag is called', async () => {
    wrapper.vm.internalTags.value = [
      { key: 'key1', value: 'value1' },
      { key: 'key2', value: 'value2' }
    ];

    wrapper.vm.removeTag(0);

    expect(wrapper.vm.internalTags.value).toEqual([
      { key: 'key2', value: 'value2' }
    ]);
  });

  it('should update tag when updateTag is called', async () => {
    wrapper.vm.internalTags.value = [
      { key: 'key1', value: 'value1' }
    ];

    wrapper.vm.updateTag(0, { key: 'updatedkey', value: 'updatedvalue' });

    expect(wrapper.vm.internalTags.value[0]).toEqual({ key: 'updatedkey', value: 'updatedvalue' });
  });

  it('should filter out empty tags when flattening', async () => {
    wrapper.vm.internalTags.value = [
      { key: 'key1', value: 'value1' },
      { key: '', value: '' },
      { key: 'key2', value: 'value2' }
    ];

    await wrapper.vm.$nextTick();

    const emittedValue = wrapper.emitted('update:modelValue')[0][0];
    expect(emittedValue).toBe('key1=value1;key2=value2');
  });
});
