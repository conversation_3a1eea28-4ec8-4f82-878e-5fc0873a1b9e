<template>
  <app-grid-form context="inline">

    <SystemTagItem
      v-for="(tag, index) in internalTags"
      :tag="tag"
      :index="index"
      :key="`${tag.key}-${index}`"
      :available-tags="availableSystemTags"
      @remove-tag="removeTag"
      @update-tag="updateTag" />

  </app-grid-form>
</template>

<script>
// Compatibility for v-model since we're on Vue 2.7
export default {
  model: {
    prop: 'modelValue',
    event: 'update:modelValue'
  }
};
</script>

<script setup>
import { ref, computed, watch } from 'vue';
import tops from '@/utils/tops';
import Access from '@/utils/access.js';
import SystemTagItem from './SystemTagItem.vue';

const props = defineProps({
  modelValue: { type: String, default: '' }
});
const emit = defineEmits(['update:modelValue']);

const internalTags = ref([]);
const availableSystemTags = ref([]);
const canEditSystemTags = computed(() => Access.has('calls.editSystemTags'));

const parseSystemTags = (value = '') =>
  value.length >= 3
    ? value
      .split(';')
      .filter(Boolean)
      .map(pair => {
        const [key = '', ...rest] = pair.split('=');
        return { key, value: rest.join('=') };
      })
    : [];

const flattenSystemTags = tags =>
  tags
    .filter(({ key, value }) => key || value)
    .map(({ key = '', value = '' }) => `${key}=${value}`)
    .join(';');

watch(
  () => parseSystemTags(props.modelValue),
  tags => {
    internalTags.value = tags;
  },
  { immediate: true }
);

watch(
  internalTags,
  tags => {
    const flattened = flattenSystemTags(tags);
    if (flattened !== props.modelValue) {
      emit('update:modelValue', flattened);
    }
  },
  { deep: true }
);

const addTag = () => {
  const defaultKey = availableSystemTags.value.length > 0
    ? availableSystemTags.value[0].ShortCode
    : '';

  internalTags.value.push({
    key: defaultKey,
    value: ''
  });
};
const removeTag = index => internalTags.value.splice(index, 1);
const updateTag = (index, tag) => internalTags.value.splice(index, 1, tag);

const getAvailableSystemTags = async () => {
  try {
    const { data: { Data } } = await tops.TOPSCompany.GetSystemDataTags();
    availableSystemTags.value = Data;
  } catch {
    availableSystemTags.value = [];
  }
};

getAvailableSystemTags();
</script>

<style scoped>
</style>
