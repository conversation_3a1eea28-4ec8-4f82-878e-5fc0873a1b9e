<template>
  <app-grid-form context="inline">
    <app-button class="_add" @click="addTag" :disabled="!canEditSystemData">
      <i class="far fa-plus"></i>
    </app-button>

    <SystemTagItem
      v-for="(tag, index) in systemTagsProxy"
      :tag="tag"
      :index="index"
      :key="tag.key"
      @remove-tag="removeTag" />

  </app-grid-form>
</template>

<script setup>
import { ref, computed } from 'vue';
import tops from '@/utils/tops';
import Access from '@/utils/access.js';
import SystemTagItem from './SystemTagItem.vue';

// defineProps({
//   tags: {
//     type: Array
//   }
// });

const systemTagsProxy = computed({
  get() {
    // return parseSystemTags(this.call?.vc255DelimitedInfo);
  },
  set(value) {
    // this.$set(this.call, 'vc255DelimitedInfo', flattenSystemTags(value));
  }
});

const canEditSystemData = computed(() => {
  return Access.has('calls.editSystemTags') && this.canEditProperty();
});

const parseSystemTags = (value = '') => {
  if (value.length < 3) return [];

  const pairs = value.split(';');
  let items = [];

  pairs.forEach(pair => {
    const [key, value] = [...pair.split('=')];

    items.push({ key, value });
  });

  return items;
}

const flattenSystemTags = (value) => {
  let pairs = [];

  value.forEach(item => {
    pairs.push([item.key, item.value].join('='));
  });

  return pairs.join(';');
}

const addTag = () => {
  systemTagsProxy.value.push({
    key: '',
    value: ''
  });
}

const removeTag = (index) => {
  // appliedDataTags.value.splice(index, 1);
};

const availableSystemTags = ref([]);
const getAvailableSystemTags = async () => {
  const response = await tops.TOPSCompany.GetSystemDataTags();
  availableSystemTags.value = response.data.Data;
};

getAvailableSystemTags();
</script>

<style scoped>
</style>
