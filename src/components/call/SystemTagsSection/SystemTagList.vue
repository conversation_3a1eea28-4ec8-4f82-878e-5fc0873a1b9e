<template>
  <app-grid-form context="inline">
    <app-button class="_add" @click="addTag" :disabled="!canEditSystemData">
      <i class="far fa-plus"></i>
    </app-button>

    <pre>{{ internalTags }}</pre>

    <SystemTagItem
      v-for="(tag, index) in internalTags"
      :tag="tag"
      :index="index"
      :key="`${tag.key}-${index}`"
      :available-tags="availableSystemTags"
      @remove-tag="removeTag"
      @update-tag="updateTag" />

  </app-grid-form>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import tops from '@/utils/tops';
import Access from '@/utils/access.js';
import SystemTagItem from './SystemTagItem.vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue']);

// Internal array of tag objects
const internalTags = ref([]);

// Available system tags from API
const availableSystemTags = ref([]);

const canEditSystemData = computed(() => {
  return Access.has('calls.editSystemTags');
});

// Parse delimited string into array of objects
const parseSystemTags = (value = '') => {
  if (!value || value.length < 3) return [];

  const pairs = value.split(';');
  const items = [];

  pairs.forEach(pair => {
    const [key, tagValue] = pair.split('=');
    if (key !== undefined && tagValue !== undefined) {
      items.push({ key: key || '', value: tagValue || '' });
    }
  });

  return items;
};

// Flatten array of objects into delimited string
const flattenSystemTags = (tags) => {
  if (!Array.isArray(tags) || tags.length === 0) return '';

  const pairs = tags
    .filter(item => item.key || item.value) // Only include non-empty tags
    .map(item => `${item.key || ''}=${item.value || ''}`);

  return pairs.join(';');
};

// Watch for changes to modelValue prop and update internal array
watch(() => props.modelValue, (newValue) => {
  internalTags.value = parseSystemTags(newValue);
}, { immediate: true });

// Watch for changes to internal array and emit to parent
watch(internalTags, (newTags) => {
  const flattened = flattenSystemTags(newTags);
  if (flattened !== props.modelValue) {
    emit('update:modelValue', flattened);
  }
}, { deep: true });

const addTag = () => {
  internalTags.value.push({
    key: '',
    value: ''
  });
};

const removeTag = (index) => {
  internalTags.value.splice(index, 1);
};

const updateTag = (index, updatedTag) => {
  if (index >= 0 && index < internalTags.value.length) {
    internalTags.value[index] = { ...updatedTag };
  }
};

const getAvailableSystemTags = async () => {
  try {
    const response = await tops.TOPSCompany.GetSystemDataTags();
    availableSystemTags.value = response.data.Data;
  } catch (error) {
    console.error('Failed to load system data tags:', error);
    availableSystemTags.value = [];
  }
};

onMounted(() => {
  getAvailableSystemTags();
});
</script>

<style scoped>
</style>
