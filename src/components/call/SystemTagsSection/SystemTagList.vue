<template>
  <app-grid-form context="inline">
    <app-button class="_add" @click="addTag" :disabled="!canEditSystemData">
      <i class="far fa-plus"></i>
    </app-button>

    <pre>internalTags: {{ internalTags }}</pre>

    <SystemTagItem
      v-for="(tag, index) in internalTags"
      :tag="tag"
      :index="index"
      :key="`${tag.key}-${index}`"
      :available-tags="availableSystemTags"
      @remove-tag="removeTag"
      @update-tag="updateTag" />

  </app-grid-form>
</template>

<script setup>
import { ref, computed, watch, onMounted, getCurrentInstance } from 'vue';
import tops from '@/utils/tops';
import Access from '@/utils/access.js';
import SystemTagItem from './SystemTagItem.vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue']);

const internalTags = ref([]);
const availableSystemTags = ref([]);

const canEditSystemData = computed(() => {
  return Access.has('calls.editSystemTags');
});

// Parse delimited string into array of objects
const parseSystemTags = (value = '') => {
  console.log('*** parseSystemTags:value', value);
  if (!value || value.length < 3) return [];

  const pairs = value.split(';');
  const items = [];

  pairs.forEach(pair => {
    // Skip empty pairs
    if (!pair.trim()) return;

    // Split on first '=' only to handle values that might contain '='
    const equalIndex = pair.indexOf('=');
    if (equalIndex === -1) {
      // No '=' found, treat as key with empty value
      items.push({ key: pair, value: '' });
    } else {
      const key = pair.substring(0, equalIndex);
      const tagValue = pair.substring(equalIndex + 1);
      items.push({ key: key || '', value: tagValue || '' });
    }
  });

  return items;
};

// Flatten array of objects into delimited string
const flattenSystemTags = (tags) => {
  if (!Array.isArray(tags) || tags.length === 0) return '';

  const pairs = tags
    .filter(item => item.key || item.value) // Only include non-empty tags
    .map(item => `${item.key || ''}=${item.value || ''}`);

  return pairs.join(';');
};

// Watch for changes to modelValue prop and update internal array
watch(() => props.modelValue, (newValue) => {
  console.log('*** watch:modelValue', newValue);
  internalTags.value = parseSystemTags(newValue);
}, { immediate: true });

// Watch for changes to internal array and emit to parent
watch(internalTags, (newTags) => {
  const flattened = flattenSystemTags(newTags);
  if (flattened !== props.modelValue) {
    emit('update:modelValue', flattened);
  }
}, { deep: true });

const addTag = () => {
  internalTags.value.push({
    key: '',
    value: ''
  });
};

const removeTag = (index) => {
  console.log('*** removeTag', index);
  internalTags.value.splice(index, 1);
};

const updateTag = (index, updatedTag) => {
  console.log('*** updateTag', index, updatedTag);
  if (index >= 0 && index < internalTags.value.length) {
    internalTags.value[index] = { ...updatedTag };
  }
};

const getAvailableSystemTags = async () => {
  try {
    const response = await tops.TOPSCompany.GetSystemDataTags();
    availableSystemTags.value = response.data.Data;
  } catch (error) {
    console.error('Failed to load system data tags:', error);
    availableSystemTags.value = [];
  }
};

// Only use onMounted if we're in a proper component context
// const instance = getCurrentInstance();
// if (instance) {
  // onMounted(() => {
  //   getAvailableSystemTags();
  // });
// } else {
  // In test environment or when not in component context, call directly
  getAvailableSystemTags();
// }
</script>

<style scoped>
</style>
