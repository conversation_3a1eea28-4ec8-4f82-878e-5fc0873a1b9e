<template>
  <SystemTagList v-model="call.vc255DelimitedInfo" />
</template>

<script>
import BaseSection from '../BaseSection.vue';
import SystemTagList from './SystemTagList.vue';
import { CALL_SECTION_SYSTEM_TAGS } from '@/config.js';

export default {
  name: CALL_SECTION_SYSTEM_TAGS,

  extends: BaseSection,

  components: {
    SystemTagList
  },

  data () {
    return {
      sectionName: CALL_SECTION_SYSTEM_TAGS
    };
  }
};
</script>

<style scoped>
</style>
