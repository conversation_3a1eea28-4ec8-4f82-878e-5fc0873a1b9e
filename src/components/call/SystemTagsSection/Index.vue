<template>
  <section id="system-tags-section">
    <div class="_header">
      <div class="_subheader">System Data</div>
    </div>

    <SystemTagList v-model="call.vc255DelimitedInfo" />
  </section>
</template>

<script>
import BaseSection from '../BaseSection.vue';
import SystemTagList from './SystemTagList.vue';
import { CALL_SECTION_SYSTEM_TAGS } from '@/config.js';

export default {
  name: CALL_SECTION_SYSTEM_TAGS,

  extends: BaseSection,

  components: {
    SystemTagList
  },

  data () {
    return {
      sectionName: CALL_SECTION_SYSTEM_TAGS
    };
  }
};
</script>

<style scoped>
#system-tags-section {
  ._header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    padding: 0.25rem 0.5rem;
    border-bottom: 1px solid var(--input-border);
    background-color: hsl(var(--blue-h), var(--blue-s), calc(var(--blue-l) + 55%));
    overflow: hidden;

    ._add {
      width: 1rem;
      font-size: var(--font-size-h5);
      background-color: transparent;
      border: 0;
    }
  }

  ._subheader {
    font-weight: bold;
  }

  ._remove {
    display: flex;
    justify-content: center;
    align-items: center;

    cursor: pointer;
  }
}
</style>
