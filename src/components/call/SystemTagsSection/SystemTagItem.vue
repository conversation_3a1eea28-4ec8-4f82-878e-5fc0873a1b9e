<template>
  <div class="_tag columns is-multiline">
    <div class="column is-5">
      <template v-if="isTagAvailable(tag)">
        <app-select v-model="tag.key" :options="dataTags" :id="tag.key" keyAlias="ShortCode" valueAlias="Name" :disabled="!canEditSystemData">
          Tag
        </app-select>
      </template>
      <template v-else>
        <app-text v-model="tag.key" :id="tag.key" :disabled="true">
          Tag
        </app-text>
      </template>
    </div>
    <div class="column is-6">
      <component :is="getValueInput(tag.key)" v-model="tag.value" :id="tag.key" :options="getValueOptions(tag.key)" :disabled="!canEditSystemData || !isTagAvailable">
        Value
      </component>
    </div>
    <div class="_remove column is-1" @click="removeTag" :disabled="!canEditSystemData || !isTagAvailable">
      <i class="far fa-trash-alt"></i>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import Access from '@/utils/access.js';

defineProps({
  tag: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  }
});

const emits = defineEmits(['remove-tag']);

const dataTags = ref([]);

const canEditSystemData = computed(() => {
  // return Access.has('calls.editSystemTags') && this.canEditProperty();
  return Access.has('calls.editSystemTags') || true;
})

const removeTag = () => {
  emit('remove-tag', index);
};

const isTagAvailable = (item) => {
  if (!item.key) return false;

  return dataTags.value.find(dataTag => dataTag.ShortCode === item.key);
}

const getValueInput = (tagKey) => {
  let targetTag = dataTags.value.find(dataTag => dataTag.ShortCode === tagKey);

  if (!targetTag) return 'app-text';

  let options = targetTag?.Options ?? [];

  return options.length ? 'app-select-simple' : 'app-text';
}

const getValueOptions = (tagKey) => {
  let targetTag = dataTags.value.find(dataTag => dataTag.ShortCode === tagKey);

  return targetTag?.Options ?? [];
}
</script>

<style scoped>
</style>
