<template>
  <div class="_tag columns is-multiline">
    <div class="column is-5">
      <template v-if="isTagAvailable">
        <app-select
          :model-value="localTag.key"
          @update:model-value="updateKey"
          :options="availableTags"
          :id="`tag-key-${index}`"
          keyAlias="ShortCode"
          valueAlias="Name"
          :disabled="!canEditSystemData">
          Tag
        </app-select>
      </template>
      <template v-else>
        <app-text
          :model-value="localTag.key"
          @update:model-value="updateKey"
          :id="`tag-key-${index}`"
          :disabled="true">
          Tag
        </app-text>
      </template>
    </div>
    <div class="column is-6">
      <component
        :is="getValueInput(localTag.key)"
        :model-value="localTag.value"
        @update:model-value="updateValue"
        :id="`tag-value-${index}`"
        :options="getValueOptions(localTag.key)"
        :disabled="!canEditSystemData || !isTagAvailable">
        Value
      </component>
    </div>
    <div class="_remove column is-1" @click="removeTag" :disabled="!canEditSystemData || !isTagAvailable">
      <i class="far fa-trash-alt"></i>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import Access from '@/utils/access.js';

const props = defineProps({
  tag: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  },
  availableTags: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['remove-tag', 'update-tag']);

// Local copy of the tag to avoid direct mutation
const localTag = ref({ ...props.tag });

// Watch for changes to the prop and update local copy
watch(() => props.tag, (newTag) => {
  localTag.value = { ...newTag };
}, { deep: true });

const canEditSystemData = computed(() => {
  return Access.has('calls.editSystemTags');
});

const isTagAvailable = computed(() => {
  if (!localTag.value.key) return true;
  return props.availableTags.find(dataTag => dataTag.ShortCode === localTag.value.key);
});

const updateKey = (newKey) => {
  localTag.value.key = newKey;
  emit('update-tag', props.index, { ...localTag.value });
};

const updateValue = (newValue) => {
  localTag.value.value = newValue;
  emit('update-tag', props.index, { ...localTag.value });
};

const removeTag = () => {
  emit('remove-tag', props.index);
};

const getValueInput = (tagKey) => {
  const targetTag = props.availableTags.find(dataTag => dataTag.ShortCode === tagKey);

  if (!targetTag) return 'app-text';

  const options = targetTag?.Options ?? [];

  return options.length ? 'app-select-simple' : 'app-text';
};

const getValueOptions = (tagKey) => {
  const targetTag = props.availableTags.find(dataTag => dataTag.ShortCode === tagKey);

  return targetTag?.Options ?? [];
};
</script>

<style scoped>
</style>
