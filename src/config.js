import repository from '../package.json';

export const PRODUCT = repository.name;
export const VERSION = repository.version;

export const ASSET_PATH = '//' + window.location.host + window.location.pathname + 'assets';
export const REQUEST_LIFESPAN = 60;

export const GRID_KEY = {
  assigned: 1039,
  unassigned: 1038,
  checkoutDriver: 1051,
  checkoutEmployee: 1052,
  checkoutCalls: 1053,
  checkoutReleases: 1054,
  lienStepCalls: 1058,
  lienSearchCalls: 1059,
  motorClubCallsToInvoice: 1065,
  motorClubInvoices: 1066,
  payment: {
    calls: 1067,
    payments: 1068
  }
};

export const FIELD_DATA_TYPE = {
  PRIMARY_KEY: 104,
  FOREIGN_KEY: 105,
  FOREIGN_KEY_STRING: 106,
  STRING: 107,
  NUMBER: 108,
  BOOLEAN: 109,
  BLOB: 110,
  DATE: 111,
  CURRENCY: 112,
  OTHER: 113
};

export const INVOICE_DATA_TYPE = {
  STRING: 1,
  NUMBER: 2,
  DATE: 3,
  BOOLEAN: 4,
  ARRAY: 5
};

export const PRODUCTKEY_WEB_PORTAL = 1000;
export const PRODUCTKEY_ADMINISTRATION = 1001;
export const PRODUCTKEY_TOWER = 1002;
export const PRODUCTKEY_MANAGER = 1003;

export const EVENT_ERROR = 'EVENT_ERROR';
export const EVENT_WARNING = 'EVENT_WARNING';
export const EVENT_INFO = 'EVENT_INFO';
export const EVENT_SUCCESS = 'EVENT_SUCCESS';
export const EVENT_APP_MODE_CHANGED = 'EVENT_APP_MODE_CHANGED';
export const EVENT_API_MODE_CHANGED = 'EVENT_API_MODE_CHANGED';
export const EVENT_USER_CHANGED = 'EVENT_USER_CHANGED';
export const EVENT_INSTANCE_CHANGED = 'EVENT_INSTANCE_CHANGED';
export const EVENT_PRODUCT_CHANGED = 'EVENT_PRODUCT_CHANGED';
export const EVENT_PRODUCTS_CHANGED = 'EVENT_PRODUCTS_CHANGED';
export const EVENT_ORG_UNIT_CHANGED = 'EVENT_ORG_UNIT_CHANGED';
export const EVENT_ORG_UNITS_CHANGED = 'EVENT_ORG_UNITS_CHANGED';
export const EVENT_ROLE_CHANGED = 'EVENT_ROLE_CHANGED';
export const EVENT_LOG_OUT = 'EVENT_LOG_OUT';
export const EVENT_LOGGED_OUT = 'EVENT_LOGGED_OUT';
export const EVENT_ALTER_CALL_COMPLETE = 'EVENT_ALTER_CALL_COMPLETE';
export const EVENT_OPEN_PAYMENTS = 'EVENT_OPEN_PAYMENTS';
export const EVENT_CLOSE_PAYMENTS = 'EVENT_CLOSE_PAYMENTS';
export const EVENT_OPEN_HOLDS = 'EVENT_OPEN_HOLDS';
export const EVENT_CLOSE_HOLDS = 'EVENT_CLOSE_HOLDS';
export const EVENT_TOGGLE_NAVIGATION = 'EVENT_TOGGLE_NAVIGATION';
export const EVENT_GET_VIEWS = 'EVENT_GET_VIEWS';
export const EVENT_SECTION_EXPANDED = 'EVENT_SECTION_EXPANDED';
export const EVENT_JUMP_BACK_SECTION = 'EVENT_JUMP_BACK_SECTION';
export const EVENT_JUMP_FORWARD_SECTION = 'EVENT_JUMP_FORWARD_SECTION';
export const BEFORE_CALL_READ = 'BEFORE_CALL_READ';
export const AFTER_CALL_READ = 'AFTER_CALL_READ';
export const EVENT_CALL_SAVING = 'EVENT_CALL_SAVING';
export const EVENT_CALL_SAVED = 'EVENT_CALL_SAVED';
export const EVENT_RESET_CALL_PRICING = 'EVENT_RESET_CALL_PRICING';
export const EVENT_PROMPT_RESET_CALL_PRICING = 'EVENT_PROMPT_RESET_CALL_PRICING';
export const EVENT_SET_CALL_PRICING_GUARD_ACTIVE = 'EVENT_SET_CALL_PRICING_GUARD_ACTIVE';
export const EVENT_TOGGLE_CALL_SECTION = 'EVENT_TOGGLE_CALL_SECTION';
export const EVENT_TOGGLE_ACCORDIAN_GROUP = 'EVENT_TOGGLE_ACCORDIAN_GROUP';
export const EVENT_SEARCH_THROW_BOOMERANG = 'EVENT_SEARCH_THROW_BOOMERANG';
export const EVENT_SEARCH_CATCH_BOOMERANG = 'EVENT_SEARCH_CATCH_BOOMERANG';
export const EVENT_TOGGLE_FLYOUT = 'EVENT_TOGGLE_FLYOUT';
export const EVENT_TRIGGER_CALL_READ = 'EVENT_TRIGGER_CALL_READ';

export const STASH_FILTER_RELAY = 'STASH_FILTER_RELAY';

export const PRODUCTS = [];
PRODUCTS[PRODUCTKEY_WEB_PORTAL] = { key: PRODUCTKEY_WEB_PORTAL, label: 'webTOPS Portal', orgUnitRequired: false };
PRODUCTS[PRODUCTKEY_ADMINISTRATION] = { key: PRODUCTKEY_ADMINISTRATION, label: 'Administration', orgUnitRequired: false };
PRODUCTS[PRODUCTKEY_TOWER] = { key: PRODUCTKEY_TOWER, label: 'Tower', orgUnitRequired: true };
PRODUCTS[PRODUCTKEY_MANAGER] = { key: PRODUCTKEY_MANAGER, label: 'Manager', orgUnitRequired: true };

// NOTE: These need to match the section IDs from the sketch view for the call
export const CALL_SECTION_CALL = 'call';
export const CALL_SECTION_SUBTERMINAL = 'subterminal';
export const CALL_SECTION_TOW = 'tow';
export const CALL_SECTION_VEHICLE = 'vehicle';
export const CALL_SECTION_MISCELLANEOUS = 'miscellaneous';
export const CALL_SECTION_TOW_PRICING = 'towpricing';
export const CALL_SECTION_TOW_PAYMENT = 'towpayment';
export const CALL_SECTION_NOTES = 'notes';
export const CALL_SECTION_TOW_DISPATCH = 'towdispatch';
export const CALL_SECTION_LEGS = 'legs';
export const CALL_SECTION_CONTACTS = 'contacts';
export const CALL_SECTION_INVENTORY = 'inventory';
export const CALL_SECTION_LIEN = 'lien';
export const CALL_SECTION_RETOW = 'retow';
export const CALL_SECTION_RETOW_DISPATCH = 'retowdispatch';
export const CALL_SECTION_HOLDS = 'holds';
export const CALL_SECTION_INSPECTIONS = 'inspections';
export const CALL_SECTION_ACCOUNTING = 'accounting';
export const CALL_SECTION_CANCEL = 'cancel';
export const CALL_SECTION_IMAGES = 'images';
export const CALL_SECTION_QUOTE = 'quote';
export const CALL_SECTION_MOTOR_CLUB_BILLING = 'motor_club_billing';
export const CALL_SECTION_SALE_PAYMENTS = 'sale_payments';
export const CALL_SECTION_SALE_PRICING = 'sale_pricing';
export const CALL_SECTION_MILEAGE = 'mileage';
export const CALL_SECTION_SYSTEM_TAGS = 'system_data';

export const CONTACT_PARENT_TYPE_KEY = 337;

export const COMPANY_ID = {
  GRAND_RAPIDS_POLICE_DEPARTMENT: 1990
};

export const ACCESS_CODE = {
  calls: {
    confirm: 3,
    confirmNoCharge: 1102, // Allow Confirmation of No Charge Calls
    reconcile: 1004, // Reconcile Calls and Call Payments
    read: 5,
    edit: 6,
    editCustomerAfterReconciled: 1011, // Allow Changing Customer After Call Reconciliation
    editControlCustomer: 1063, // 1063  Allow Changing the Control Customer
    editCallTaken: 1067, // Allow Changing the Call Taken and Dispatch Assigned Dates and Times
    quickClear: 1100, // Allow quick clear for New Calls
    accountingNotes: 1095, // Alias of Access to Accounting Notes and Call Price
    price: 1095, // Alias of Access to Accounting Notes and Call Price
    dateInGreaterThanDispatchComplete: 1121, // (Enforced server-side)
    duplicate: 1219, // No Duplication of Call
    editDiscounts: 1008, // Modify Discount % and Create No Charge Calls
    editPoliceNumber: 1117,
    restrictUserDefined3: 1220, // No Editing of Call User Defined 3
    restrictDispatch: 1221, // No Access to Dispatch section
    restrictSale: 1222, // No Access to Sale section
    restrictAccounting: 1223, // No Access to Accounting section
    restrictCancel: 1224, // No Access to Cancel Call section
    editInventoryInOut: 1225, // Edit Inventory Date In and Date Out
    notes: 1234, // Read Notes
    editHoldUntil: 1210, // Edit Hold Until Date
    releaseHold: 10060, // Allow Releasing Hold
    deleteHold: 10061, // Allow Deleting Hold
    editNotesAfterConfirmed: 1275, // Edit Notes After Confirmation
    partialEditAfterConfirmed: 1036, // Allow Editing Call After Confirmation (limited fields)
    fullEditAfterConfirmed: 1239, // Edit All Fields After Confirmation
    editSystemTags: 1089 // Allow Editing of System Data
  },

  dispatches: {
    read: 9,
    manage: 10,
    editCommissionAmounts: 1007, // Access to Fixed Commission Amounts
    editAssigned: 1067 // Allow Changing the Call Taken and Dispatch Assigned Dates and Times
  },

  dmv: {
    access: 91
  },

  drivers: {
    read: 13,
    edit: 14,
    delete: 16,
    commission: 1000, // Access To Driver Commissions
    editStatusOnly: 1212 // Edit Truck and Driver Status Only
  },

  employees: {
    read: 21, // Read Local Employees (Location Level) Employee
    edit: 22, // Edit Local Employees (Location Level) Employee
    delete: 24 // Delete Local Employees (Location Level) Employee
  },

  customers: {
    read: 1,
    edit: 2,
    delete: 4,
    editEssentials: 1001, // Advanced Edit - Name, Short Code, Status, Type
    export: 1056,
    editCreditLimit: 1060,
    editTerms: 1088,
    editPPI: 1114, // This is the PPI Name/Phone section. Without this right, it's read only.
    viewTaxAndDiscount: 1217, // If they don't have this then those fields are invisible
    restrictToAuction: 1226
  },

  inspections: {
    edit: 90
  },

  liens: {
    readSetup: 1045, // Read Lien Setup / Lien Setup / Read
    editSetup: 1046, // Edit Lien Setup / Lien Setup / Edit
    readBatch: 1047, // Read Lien / Lien / Read
    editBatch: 1048, // Edit Lien / Lien / Edit
    readOnCall: 1211, // View Lien Details / Call / Read - Advanced
    editOnCall: 1049, // Edit Lien Information for Calls / Call / Edit - Advanced
    terminate: 1058, // Allow Termination of the Lien Process / Call / Edit - Advanced
    isReadyCallsVisible: 1227 // Allow Get All Calls Available For Lien Process Button / Lien / Edit - Advanced
  },

  lots: {
    read: 45,
    edit: 46,
    delete: 48
  },

  payments: {
    edit: 509, // Edit non reconciled payments created by user
    forceEdit: 1009, // Edit non reconciled payments not created by user
    delete: 1085, // Allow deleting payments
    forceDelete: 527, // Delete all payments regardless of payment or call state
    undoAfterReconciled: 1012, // Allow undo and company change after payment reconciliation
    historicalReceivedDate: 10049, // Allow payments with received date less than current date and time enforced server side
    restrictNegativePayments: 10227, // Do not allow negative payments
    canRefund: 1104
  },

  precollections: {
    access: 92
  },

  price: {
    read: 65,
    edit: 66,
    delete: 68
  },

  prices: {
    overrideRates: 1003, // Override a rate that is Not Overridable
    editDispatchAfterReconciled: 1005, // Allow Pricing Modifications After Reconciliation
    overrideTax: 1043, // Override Tax Rate for Calls
    editDiscountable: 1052, // Update Discountable Flag
    editTaxable: 1053, // Update Taxable Flag
    editCommissionable: 1054, // Update Commissionable Flag
    editDescription: 1214, // Edit Pricing Description
    restrictDelete: 1218, // No Pricing Item Deletion After Dispatch Completion
    allServices: 1228, // Allow All Services to be Available for Call Pricing
    restrictOrderLineDetail: 1276, // Mask Order Line Pricing (can see summary, not details)
    editSurchargeable: 10055, // Update Surchargeable Flag in Pricing Screen
    bypassPricingPolarity: 10282 // Ignore Positive/Negative Pricing Flags
  },

  txiCustomer: {
    manage: 9001
  },

  trucks: {
    read: 17,
    edit: 18,
    delete: 20,
    editStatusOnly: 1212, // Edit truck and driver status only
    decodeVin: 1120,
    forceRelease: 1215 // Allow Releasing Vehicle still in Lien Process
  },

  reports: {
    read: 49
  },

  users: {
    manage: 505
  },

  checkout: {
    read: 41,
    batchQueue: 10258
  },

  forceUndo: 1057, // Allow Undo After Call Reconciliation
  undo: 1010, // Allow Undo (Place Back In)
  deleteContacts: 1229, // Allow Deleting Contacts
  delimitedInformation: 1235, // Read Delimited Info

  tickets: {
    read: 57,
    edit: 58,
    delete: 60,
    add: 1230,
    assignToDriversOnly: 1002
  },

  images: {
    add: 10146,
    view: 1240,
    edit: 10147
  },

  quote: {
    read: 545,
    write: 546
  },

  motorClubBilling: {
    read: 547,
    edit: 548
  },

  inventory: {
    allowFinalDisposition: 1013 // Allow Final Disposition of Auction or Scrap for Inventory Vehicles
  },

  towlien: {
    manage: 10281
  }

  // @TODO
  // 1006  Create Sublet Calls
  // 1103  Allow Unconfirming Calls that have Not been Transferred
  // 1123  Allow Use of Drivers License Swipe Device
  // 1125  Allow Viewing and Editing of Call Collections Information
  // 1232  Read Invoices
  // 1233  Read Invoice Headers Only
  // 1261  DSO - Add Call
};

export const VALUE_ID = {
  callStatus: {
    awaitingRetow: 116,
    cancelled: 114,
    completed: 115,
    confirmed: 316,
    dispatched: 110,
    intercompany: 151,
    inventory: 111,
    retow: 112,
    retowDispatch: 113,
    sublet: 152,
    unassigned: 109
  },
  driverStatus: {
    onDuty: 1,
    offDuty: 2
  },
  truckStatus: {
    available: 67,
    outOfService: 68
  },
  customerType: {
    ppi: 308,
    account: 32,
    auction: 174,
    motorClub: 176,
    accountOther: 272
  },
  customerStatus: {
    approved: 36
  },
  inspectionValueType: {
    suggestions: 197, // This has a list of options but they're just suggestions. So Free Form Text is allowed as well
    dateTime: 775, // Use the calendar option here with the current date/time as the default
    text: 194,
    multiSelect: 199, // Results in a CSV string. Not currently used
    number: 195,
    select: 198,
    trueFalse: 196, // "True" and "False" are the only options
    yesNo: 909 // "Yes" and "No" are the only options
  },
  lienTaskType: {
    export: 222,
    import: 221,
    label: 224,
    letter: 223,
    other: 226,
    report: 225,
    setDate: 235
  },
  lienTask: {
    changeProcess: 48
  },
  stepStatus: {
    waiting: 216,
    activated: 217,
    skipped: 218,
    onHold: 219,
    completed: 220
  },
  lienStatus: {
    acquired: 215,
    inProcess: 213,
    terminated: 214
  },
  paymentReceiptType: {
    receivable: 317,
    driver: 145,
    lot: 148,
    office: 147,
    pending: 310,
    retowDriver: 146
  },
  paymentType: {
    arReceivable: 334,
    cash: 103,
    creditCard: 105,
    check: 104
  },
  pricingType: {
    adjustment: 248,
    call: 128,
    dispatch: 129,
    retow: 131,
    storage: 130,
    sale: 247
  },
  ticketStatus: {
    lost: 71,
    open: 69,
    used: 70,
    voided: 72
  },
  dispatchStatus: {
    assigned: 94,
    dispatched: 95,
    acknowledged: 96,
    arrived: 97,
    hooked: 98,
    dropped: 99,
    completed: 100
  },
  creditCardProcessingType: {
    none: 299,
    eXact: 300,
    towpay: 943
  },
  disposition: {
    released: 3,
    auctioned: 4,
    scrapped: 5,
    sold: 207
  }
};
